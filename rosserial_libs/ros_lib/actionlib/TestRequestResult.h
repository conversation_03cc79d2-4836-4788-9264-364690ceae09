#ifndef _ROS_actionlib_TestRequestResult_h
#define _ROS_actionlib_TestRequestResult_h

#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "ros/msg.h"

namespace actionlib
{

  class TestRequestResult : public ros::Msg
  {
    public:
      int32_t the_result;
      bool is_simple_server;

    TestRequestResult():
      the_result(0),
      is_simple_server(0)
    {
    }

    virtual int serialize(unsigned char *outbuffer) const
    {
      int offset = 0;
      union {
        int32_t real;
        uint32_t base;
      } u_the_result;
      u_the_result.real = this->the_result;
      *(outbuffer + offset + 0) = (u_the_result.base >> (8 * 0)) & 0xFF;
      *(outbuffer + offset + 1) = (u_the_result.base >> (8 * 1)) & 0xFF;
      *(outbuffer + offset + 2) = (u_the_result.base >> (8 * 2)) & 0xFF;
      *(outbuffer + offset + 3) = (u_the_result.base >> (8 * 3)) & 0xFF;
      offset += sizeof(this->the_result);
      union {
        bool real;
        uint8_t base;
      } u_is_simple_server;
      u_is_simple_server.real = this->is_simple_server;
      *(outbuffer + offset + 0) = (u_is_simple_server.base >> (8 * 0)) & 0xFF;
      offset += sizeof(this->is_simple_server);
      return offset;
    }

    virtual int deserialize(unsigned char *inbuffer)
    {
      int offset = 0;
      union {
        int32_t real;
        uint32_t base;
      } u_the_result;
      u_the_result.base = 0;
      u_the_result.base |= ((uint32_t) (*(inbuffer + offset + 0))) << (8 * 0);
      u_the_result.base |= ((uint32_t) (*(inbuffer + offset + 1))) << (8 * 1);
      u_the_result.base |= ((uint32_t) (*(inbuffer + offset + 2))) << (8 * 2);
      u_the_result.base |= ((uint32_t) (*(inbuffer + offset + 3))) << (8 * 3);
      this->the_result = u_the_result.real;
      offset += sizeof(this->the_result);
      union {
        bool real;
        uint8_t base;
      } u_is_simple_server;
      u_is_simple_server.base = 0;
      u_is_simple_server.base |= ((uint8_t) (*(inbuffer + offset + 0))) << (8 * 0);
      this->is_simple_server = u_is_simple_server.real;
      offset += sizeof(this->is_simple_server);
     return offset;
    }

    const char * getType(){ return "actionlib/TestRequestResult"; };
    const char * getMD5(){ return "61c2364524499c7c5017e2f3fce7ba06"; };

  };

}
#endif