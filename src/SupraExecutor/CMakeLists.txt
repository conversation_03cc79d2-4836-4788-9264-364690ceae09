CMAKE_MINIMUM_REQUIRED( VERSION 3.0.0 FATAL_ERROR )
MESSAGE(STATUS "Building SUPRA Executor")

# if ITK is used we need to include this for each executable
include(supraIncludeITK)

############################################
#Program base source files
SET(SUPRA_EXECUTOR_SOURCE 
	main.cpp)
SET(SUPRA_EXECUTOR_HEADERS)
	
############################################
#Build Executor
SOURCE_GROUP(src FILES ${SUPRA_EXECUTOR_SOURCE})
SOURCE_GROUP(inc FILES ${SUPRA_EXECUTOR_HEADERS})

INCLUDE_DIRECTORIES(SUPRA_EXECUTOR
	${SUPRA_Lib_INCLUDEDIRS}
)
LINK_DIRECTORIES(SUPRA_EXECUTOR
	${SUPRA_Lib_LIBDIRS}
)

ADD_EXECUTABLE(SUPRA_EXECUTOR
	${SUPRA_EXECUTOR_SOURCE}
	${SUPRA_EXECUTOR_HEADERS}
)
TARGET_COMPILE_DEFINITIONS(SUPRA_EXECUTOR
	PRIVATE ${SUPRA_Lib_DEFINES})
TARGET_LINK_LIBRARIES(SUPRA_EXECUTOR
	${SUPRA_Lib_LIBRARIES}
)
set_property(TARGET SUPRA_EXECUTOR PROPERTY CXX_STANDARD 11)
set_property(TARGET SUPRA_EXECUTOR PROPERTY CXX_STANDARD_REQUIRED ON)

add_dependencies(SUPRA_EXECUTOR SUPRA_Lib)

INSTALL(TARGETS SUPRA_EXECUTOR
  RUNTIME
  DESTINATION bin
  COMPONENT applications)
