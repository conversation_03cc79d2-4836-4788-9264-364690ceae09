<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>934</width>
    <height>775</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="MinimumExpanding" vsizetype="MinimumExpanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_2">
    <item>
     <widget class="QWidget" name="widget" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QGroupBox" name="groupBoxControl">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="title">
          <string>Control</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <widget class="QPushButton" name="pushButtonLoad">
            <property name="text">
             <string>Load config</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QPushButton" name="pushButtonSequence">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Sequence Start</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButtonFreeze">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Freeze</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QPushButton" name="pushButtonResetFreeze">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Reset Freeze</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="pushButtonStop">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Stop</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="pushButtonStart">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>Start</string>
            </property>
           </widget>
          </item>
          <item row="0" column="6">
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::MinimumExpanding</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="group_parameters">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="title">
          <string>Parameters</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="QScrollArea" name="scrollArea_parameters">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="horizontalScrollBarPolicy">
             <enum>Qt::ScrollBarAlwaysOff</enum>
            </property>
            <property name="widgetResizable">
             <bool>true</bool>
            </property>
            <widget class="QWidget" name="scrollAreaWidgetContents_parameters">
             <property name="geometry">
              <rect>
               <x>0</x>
               <y>0</y>
               <width>288</width>
               <height>578</height>
              </rect>
             </property>
            </widget>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="widget_2" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>1</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QSplitter" name="splitter">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="childrenCollapsible">
          <bool>false</bool>
         </property>
         <widget class="QGroupBox" name="group_allNodes">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>600</width>
            <height>240</height>
           </size>
          </property>
          <property name="title">
           <string>All Nodes</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout"/>
         </widget>
         <widget class="QGroupBox" name="group_previews">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>10</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="title">
           <string>Previews</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
          </property>
          <layout class="QHBoxLayout" name="previewWidgets_layout">
           <property name="spacing">
            <number>3</number>
           </property>
           <property name="leftMargin">
            <number>3</number>
           </property>
           <property name="topMargin">
            <number>3</number>
           </property>
           <property name="rightMargin">
            <number>3</number>
           </property>
           <property name="bottomMargin">
            <number>3</number>
           </property>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>934</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionLoadConfig"/>
    <addaction name="actionWriteConfig"/>
   </widget>
   <widget class="QMenu" name="menuLogLevel">
    <property name="title">
     <string>LogLevel</string>
    </property>
    <addaction name="actionLoglevelLog"/>
    <addaction name="actionLoglevelInfo"/>
    <addaction name="actionLoglevelWarning"/>
    <addaction name="actionLoglevelError"/>
    <addaction name="actionLoglevelExternals"/>
    <addaction name="actionLoglevelParameter"/>
   </widget>
   <widget class="QMenu" name="menuPreview">
    <property name="title">
     <string>Preview</string>
    </property>
    <addaction name="actionPreviewSizeSmall"/>
    <addaction name="actionPreviewSizeMedium"/>
    <addaction name="actionPreviewSizeLarge"/>
    <addaction name="actionPreviewSizeWambo"/>
    <addaction name="separator"/>
    <addaction name="actionPreviewLinearInterpolation"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuLogLevel"/>
   <addaction name="menuPreview"/>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <action name="actionLoadConfig">
   <property name="text">
    <string>load config</string>
   </property>
  </action>
  <action name="actionLoglevelLog">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Log</string>
   </property>
  </action>
  <action name="actionLoglevelInfo">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Info</string>
   </property>
  </action>
  <action name="actionLoglevelWarning">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Warning</string>
   </property>
  </action>
  <action name="actionLoglevelError">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Error</string>
   </property>
  </action>
  <action name="actionPreviewSizeSmall">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Small</string>
   </property>
  </action>
  <action name="actionPreviewSizeMedium">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Medium</string>
   </property>
  </action>
  <action name="actionPreviewSizeLarge">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Large</string>
   </property>
  </action>
  <action name="actionPreviewSizeWambo">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Wambo</string>
   </property>
  </action>
  <action name="actionPreviewLinearInterpolation">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Linear interpolation</string>
   </property>
  </action>
  <action name="actionLoglevelExternals">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Externals</string>
   </property>
  </action>
  <action name="actionLoglevelParameter">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Parameter</string>
   </property>
  </action>
  <action name="actionWriteConfig">
   <property name="text">
    <string>write config</string>
   </property>
  </action>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
