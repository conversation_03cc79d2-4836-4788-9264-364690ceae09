// ================================================================================================
// 
// If not explicitly stated: Copyright (C) 2011-2016, all rights reserved,
//      <PERSON> 
//		EmaiL <EMAIL>
//      Chair for Computer Aided Medical Procedures
//      Technische Universität München
//      Boltzmannstr. 3, 85748 Garching b. München, Germany
//	and
//		Rüdiger Göbl
//		Email <EMAIL>
//
// ================================================================================================

#include "RecordObject.h"

namespace supra
{
	/*const char* RecordObjectTypeToString(RecordObjectType t)
	{
		switch (t)
		{
		case TypeSyncRecordObject:
			return "TypeSyncRecordObject";
		case TypeTrackerDataSet:
			return "TypeTrackerDataSet";
		case TypeUSImage:
			return "TypeUSImage";
		case TypeRecordUnknown:
			return "TypeRecordUnknown";
		default:
			return "ERROR_TYPE";
		}
	}*/
}