<?xml version="1.0"?>
<supra_config>
	<devices>
		<inputs>
			<input type="UltrasoundInterfaceCephasonics" id="US-Cep">
				<param name="xmlFileName" type="string">
					/usr/local/cusdk/data/xscan/CPLA12875/CPLA12875_Arterial_B_noEnhance.xml
				</param>
			</input>
		</inputs>
		<outputs>
			<!--<output type="OpenIGTLinkOutputDevice" id="IGTL" />-->
			<output type="MetaImageOutputDevice" id="MHD_Raw">
				<param name="createSequences" type="bool">
					1
				</param>
				<param name="maxElements" type="uint32_t">
					250
				</param>
				<param name="filename" type="string">
					rawData
				</param>
			</output>
			<output type="MetaImageOutputDevice" id="MHD_Beam">
				<param name="createSequences" type="bool">
					1
				</param>
				<param name="maxElements" type="uint32_t">
					50
				</param>
				<param name="filename" type="string">
					beamformed
				</param>
			</output>
		</outputs>
		<nodes>
			<node type="BeamformingNode" id="BEAM">
			</node>
		</nodes>
	</devices>
	<connections>
		<connection>
			<from id="US-Cep" port="0" />
			<to id="BEAM" port="0" />
		</connection>
		<connection>
			<from id="US-Cep" port="0" />
			<to id="MHD_Beam" port="0" />
		</connection>
		<!--<connection>
			<from id="BEAM" port="0" />
			<to id="MHD_Beam" port="0" />
		</connection>-->
	</connections>
</supra_config>
