# SUPRA 超声软件工程项目技术文档

## 1. 项目概述

### 1.1 项目简介

SUPRA (Software Defined Ultrasound Processing for Real-Time Applications) 是一个开源的软件定义超声处理框架，专为实时超声图像处理应用而设计。该项目由德国慕尼黑工业大学计算机辅助医疗程序实验室开发，旨在提供一个高度模块化、可扩展的超声处理平台。

### 1.2 核心特性

- **实时处理能力**: 支持2D/3D超声图像的实时处理
- **模块化设计**: 基于流水线的处理架构，支持灵活的节点组合
- **GPU加速**: 利用CUDA进行高性能并行计算
- **多接口支持**: 提供GUI、命令行、ROS、REST等多种用户接口
- **配置驱动**: 通过XML配置文件动态定义处理流水线
- **跨平台**: 支持Linux、Windows、macOS等多个平台

### 1.3 技术栈

- **编程语言**: C++11, CUDA C++
- **并行计算**: Intel TBB (Thread Building Blocks), CUDA
- **图形界面**: Qt5, NodeEditor
- **构建系统**: CMake
- **依赖库**: OpenMP, Boost, OpenIGTLink, PyTorch (可选)
- **通信协议**: ROS, REST API, OpenIGTLink

## 2. 架构设计

### 2.1 整体架构

SUPRA采用分层架构设计，主要包含以下层次：

```mermaid
graph TB
    A[用户接口层] --> B[核心处理层]
    B --> C[设备抽象层]
    
    A1[GraphicInterface<br/>Qt GUI] --> B
    A2[CommandlineInterface<br/>命令行] --> B
    A3[RosInterface<br/>ROS集成] --> B
    A4[RestInterface<br/>REST API] --> B
    
    B --> B1[SupraManager<br/>流水线管理]
    B --> B2[AbstractNode<br/>节点基类]
    B --> B3[Container<br/>内存管理]
    B --> B4[InterfaceFactory<br/>工厂模式]
    
    C --> C1[输入设备<br/>AbstractInput]
    C --> C2[处理节点<br/>AbstractNode]
    C --> C3[输出设备<br/>AbstractOutput]
```

### 2.2 核心设计原则

1. **流水线式处理**: 数据流经有向图结构的处理节点
2. **关注点分离**: 核心逻辑与用户界面完全解耦
3. **配置驱动**: 通过XML文件动态配置处理流程
4. **工厂模式**: 支持运行时动态创建和注册新节点类型

### 2.3 数据流架构

```mermaid
graph LR
    A[输入设备] --> B[预处理节点]
    B --> C[波束合成]
    C --> D[图像处理]
    D --> E[后处理]
    E --> F[输出设备]
    
    A1[UltrasoundInterface] --> B1[RawDelayNode]
    B1 --> C1[BeamformingNode]
    C1 --> D1[ScanConverterNode]
    D1 --> E1[LogCompressorNode]
    E1 --> F1[MetaImageOutput]
```

## 3. 核心模块分析

### 3.1 SupraManager - 流水线管理器

SupraManager是整个框架的核心控制器，采用单例模式设计：

**主要功能**:
- XML配置文件解析和流水线构建
- 节点生命周期管理
- TBB流图连接管理
- 系统启动和停止控制

**关键方法**:
```cpp
class SupraManager {
public:
    static std::shared_ptr<SupraManager> Get();
    void readFromXml(const std::string& filename);
    void connect();
    void startInputs();
    void startOutputs();
    void stopAndWaitInputs();
};
```

### 3.2 Container - 统一内存管理

Container类提供了CPU和GPU内存的统一管理接口：

**设计特点**:
- 支持三种内存位置：Host、GPU、Both (统一内存)
- 自动内存池管理，减少分配开销
- CUDA流同步和事件管理
- 垃圾回收机制

**内存位置类型**:
```cpp
enum ContainerLocation {
    LocationHost,    // CPU内存
    LocationGpu,     // GPU内存  
    LocationBoth,    // CUDA统一内存
    LocationINVALID
};
```

### 3.3 AbstractNode - 节点基类

所有处理节点的抽象基类，定义了统一的接口：

**核心接口**:
```cpp
class AbstractNode {
public:
    virtual void initializeNode() = 0;
    virtual bool ready() = 0;
    virtual std::string getTypeName() = 0;
    
protected:
    virtual void configurationEntryChanged(const std::string& configKey) = 0;
    virtual void configurationChanged() = 0;
};
```

### 3.4 数据结构

#### USImage - 超声图像数据结构
```cpp
class USImage : public RecordObject {
private:
    std::shared_ptr<const USImageProperties> m_pImageProperties;
    std::shared_ptr<ContainerBase> m_pData;
    int m_dimensions;  // 2D或3D
    vec3s m_size;      // 图像尺寸
};
```

#### TrackerData - 跟踪数据结构
用于存储位置跟踪信息，支持多种跟踪设备的数据格式。

## 4. 处理节点与算法

### 4.1 波束合成算法

#### RxBeamformerCuda - CUDA波束合成器

**支持的算法**:
- **DelayAndSum**: 经典延迟求和波束合成
- **CoherenceFactorDelayAndSum**: 相干因子增强的延迟求和
- **DelayAndStdDev**: 延迟和标准差波束合成
- **TestSignal**: 测试信号生成

**CUDA并行化策略**:
```cpp
// 核函数配置
dim3 blockSize(1, 256);
dim3 gridSize(
    (numRxScanlines + blockSize.x - 1) / blockSize.x,
    (numZs + blockSize.y - 1) / blockSize.y
);

// 启动核函数
rxBeamformingDTSPACEKernel<SampleBeamformer><<<gridSize, blockSize, 0, stream>>>(
    numTransducerElements, numReceivedChannels, numTimesteps, RF,
    numTxScanlines, numRxScanlines, scanlines, numZs, zs, x_elems,
    speedOfSound, dt, additionalOffset, F, windowFunction, s, elementToChannelMap
);
```

#### 延迟求和算法实现
```cpp
template <bool interpolateRFlines, typename RFType, typename ResultType>
__device__ ResultType sampleBeamform2D(...) {
    ResultType sample = 0;
    float weightAcum = 0;
    int numAdds = 0;
    
    for (uint32_t elemIdxX = 0; elemIdxX < numTransducerElements; elemIdxX++) {
        LocationType x_elem = x_elemsDT[elemIdxX];
        if (abs(x_elem - scanline_x) <= aDT) {
            float weight = windowFunction->get((x_elem - scanline_x) * invMaxElementDistance);
            weightAcum += weight;
            numAdds++;
            
            if (interpolateRFlines) {
                LocationType delayf = initialDelay + 
                    computeDelayDTSPACE_D(dirX, dirY, dirZ, x_elem, scanline_x, depth) + additionalOffset;
                int32_t delay = static_cast<int32_t>(floor(delayf));
                delayf -= delay;
                
                if (delay < (numTimesteps - 1)) {
                    sample += weight * ((1.0f - delayf) * RF[delay + channelIdx*numTimesteps] +
                                       delayf * RF[(delay + 1) + channelIdx*numTimesteps]);
                }
            }
        }
    }
    
    return (numAdds > 0) ? sample / weightAcum * numAdds : 0;
}
```

### 4.2 图像处理节点

#### ScanConverterNode - 扫描转换
将极坐标扫描线数据转换为笛卡尔坐标图像。

#### LogCompressorNode - 对数压缩
对超声图像进行对数压缩，增强动态范围。

#### MedianFilterCudaNode - 中值滤波
CUDA实现的中值滤波器，用于图像降噪。

### 4.3 高级处理算法

#### TorchNode - PyTorch集成
支持集成PyTorch深度学习模型进行超声图像处理。

#### BilateralFilterCudaNode - 双边滤波
保边降噪的双边滤波算法CUDA实现。

## 5. 用户接口模块

### 5.1 GraphicInterface - Qt图形界面

**主要特性**:
- 基于Qt5和NodeEditor的可视化流水线编辑
- 实时参数调整和预览
- 多窗口图像显示
- 节点图形化连接

**核心组件**:
```cpp
class MainWindow : public QMainWindow {
    Q_OBJECT
public:
    void startNodes();
    void stopNodes();
    void loadConfiguration(const QString& filename);
    
private:
    Ui::MainWindow *ui;
    QtNodes::FlowScene *m_scene;
    previewBuilderQT *m_previewBuilder;
};
```

### 5.2 CommandlineInterface - 命令行界面

**功能特性**:
- 非图形化运行环境
- 脚本化批处理支持
- 实时参数修改
- 节点状态监控

**使用示例**:
```bash
./SUPRA_CMD -c config/configDemo.xml
```

### 5.3 RosInterface - ROS集成

**ROS服务**:
- `get_nodes`: 获取节点列表
- `get_node_parameters`: 获取节点参数
- `set_node_parameter`: 设置节点参数
- `sequence`: 序列控制

**消息类型**:
- `supra_msgs/freeze`: 冻结控制
- `sensor_msgs/Image`: 图像数据发布

### 5.4 RestInterface - REST API

**API端点**:
- `GET /nodes`: 获取所有节点
- `GET /nodes/{nodeId}/parameters`: 获取节点参数
- `PUT /nodes/{nodeId}/parameters/{paramName}`: 设置参数值
- `POST /control/start`: 启动处理
- `POST /control/stop`: 停止处理

**响应格式**:
```json
{
    "nodeIDs": ["input1", "beamformer1", "output1"],
    "status": "success"
}
```

## 6. 构建系统与配置

### 6.1 CMake构建选项

**主要构建选项**:
```cmake
OPTION(SUPRA_CUDA                  "Use cuda in SUPRA"                 OFF)
OPTION(SUPRA_INTERFACE_GRAPHIC     "Build Graphic Interface (QT)"      ON)
OPTION(SUPRA_INTERFACE_ROS         "Build ROS Interface"               OFF)
OPTION(SUPRA_INTERFACE_REST        "Build REST Interface"              OFF)
OPTION(SUPRA_BEAMFORMER            "Build Software beamformer"         ON)
OPTION(SUPRA_DEVICE_CEPHASONICS    "Build Cephasonics interface"       OFF)
OPTION(SUPRA_TORCH                 "Use pytorch in SUPRA"              OFF)
```

### 6.2 依赖管理

**核心依赖**:
- **Intel TBB**: 并行计算和数据流图
- **OpenMP**: 多线程并行化
- **Qt5**: 图形用户界面 (可选)
- **CUDA**: GPU计算 (可选)
- **Boost**: 系统库和网络通信
- **OpenIGTLink**: 医疗设备通信 (可选)

**CUDA配置**:
```cmake
IF(SUPRA_CUDA)
    set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS};-Wno-deprecated-gpu-targets;--use_fast_math;--default-stream=per-thread;-lineinfo")
    CUDA_SELECT_NVCC_ARCH_FLAGS(ARCH_FLAGS "Auto")
    LIST(APPEND CUDA_NVCC_FLAGS ${ARCH_FLAGS})
ENDIF()
```

### 6.3 平台支持

**支持的平台**:
- Linux (Ubuntu 16.04+, CentOS 7+)
- Windows (Visual Studio 2015+)
- macOS (10.12+)

**编译器要求**:
- GCC 5.4+ / Clang 3.8+ / MSVC 2015+
- CUDA 10.0+ (如果启用CUDA)
- C++11标准支持

## 7. 配置文件格式

### 7.1 XML配置结构

```xml
<?xml version="1.0" encoding="utf-8"?>
<supra>
    <nodes>
        <node type="UltrasoundInterfaceRawDataMock" id="US">
            <param name="filename">data/mockData.xml</param>
            <param name="speedOfSound">1540</param>
        </node>
        
        <node type="BeamformingNode" id="BEAM">
            <param name="rxBeamformer">DelayAndSum</param>
            <param name="fNumber">1.5</param>
            <param name="interpolateRFlines">true</param>
        </node>
        
        <node type="ScanConverterNode" id="SCAN">
            <param name="outputSize">[512, 512]</param>
            <param name="interpolation">linear</param>
        </node>
    </nodes>
    
    <connections>
        <connection from="US" to="BEAM"/>
        <connection from="BEAM" to="SCAN"/>
    </connections>
</supra>
```

### 7.2 参数类型系统

**支持的参数类型**:
- `bool`: 布尔值
- `int`: 整数
- `double`: 浮点数
- `string`: 字符串
- `DataType`: 数据类型枚举
- `vec2s/vec3s`: 向量类型

## 8. 使用指南

### 8.1 编译安装

```bash
# 克隆项目
git clone https://github.com/IFL-CAMP/supra.git
cd supra

# 创建构建目录
mkdir build && cd build

# 配置构建 (启用CUDA和GUI)
cmake .. -DSUPRA_CUDA=ON -DSUPRA_INTERFACE_GRAPHIC=ON

# 编译
make -j$(nproc)
```

### 8.2 运行示例

**GUI模式**:
```bash
./src/GraphicInterface/SUPRA_GUI -c ../config/configDemo.xml
```

**命令行模式**:
```bash
./src/CommandlineInterface/SUPRA_CMD ../config/configRawDataMock.xml
```

**ROS模式**:
```bash
./src/RosInterface/SUPRA_ROS ../config/configDemo.xml localhost
```

### 8.3 开发新节点

1. **创建节点类**:
```cpp
class MyFilterNode : public AbstractNode {
public:
    MyFilterNode(tbb::flow::graph& graph, const std::string& nodeID);
    
protected:
    void configurationEntryChanged(const std::string& configKey) override;
    void configurationChanged() override;
    
private:
    std::unique_ptr<tbb::flow::function_node<...>> m_node;
};
```

2. **注册到工厂**:
```cpp
// 在InterfaceFactory.cpp中添加
if (nodeType == "MyFilterNode") {
    retVal = std::make_shared<MyFilterNode>(*pG, nodeID);
}
```

3. **配置参数**:
```cpp
void MyFilterNode::configurationChanged() {
    m_filterSize = m_configurationDictionary->get<int>("filterSize");
    m_threshold = m_configurationDictionary->get<double>("threshold");
}
```

## 9. 技术亮点

### 9.1 高性能计算

- **CUDA并行化**: 充分利用GPU并行计算能力
- **TBB数据流图**: 高效的多线程流水线处理
- **内存池管理**: 减少内存分配开销
- **零拷贝优化**: 最小化数据传输开销

### 9.2 可扩展性设计

- **插件化架构**: 支持运行时加载新节点
- **工厂模式**: 简化新组件的集成
- **配置驱动**: 无需重编译即可调整处理流程
- **多接口支持**: 适应不同的使用场景

### 9.3 实时性保证

- **流式处理**: 支持连续数据流处理
- **低延迟设计**: 优化的数据路径和算法
- **GPU流同步**: 高效的异步计算管理
- **内存预分配**: 避免运行时内存分配

## 10. 总结

SUPRA是一个设计精良的超声处理框架，具有以下显著优势：

1. **模块化设计**: 高度解耦的架构便于维护和扩展
2. **高性能**: 充分利用现代硬件的并行计算能力
3. **灵活配置**: 支持多种使用场景和部署方式
4. **开源生态**: 活跃的社区和持续的开发支持

该框架为超声图像处理研究和应用开发提供了强大的基础平台，特别适合需要实时处理能力和高度定制化的应用场景。
