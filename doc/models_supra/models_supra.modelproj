﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{f55f9f16-a3b7-47f7-8c8c-3ecd6b792beb}</ProjectGuid>
    <ArchitectureToolsVersion>2.0.0.0</ArchitectureToolsVersion>
    <Name>models_supra</Name>
    <RootNamespace>models-camp-us2</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(VSToolsPath)\ArchitectureTools\Microsoft.VisualStudio.TeamArchitect.ModelingProject.targets" Condition="'$(VSToolsPath)' != ''" />
  <ItemGroup>
    <Content Include="SequenceInputDeviceInit.sequencediagram">
      <SubType>Content</SubType>
    </Content>
    <Content Include="SequenceInputDeviceInit.sequencediagram.layout">
      <SubType>Content</SubType>
      <DependentUpon>SequenceInputDeviceInit.sequencediagram</DependentUpon>
    </Content>
    <Content Include="SequenceInputStreamCallback.sequencediagram">
      <SubType>Content</SubType>
    </Content>
    <Content Include="SequenceInputStreamCallback.sequencediagram.layout">
      <SubType>Content</SubType>
      <DependentUpon>SequenceInputStreamCallback.sequencediagram</DependentUpon>
    </Content>
    <Content Include="SequenceInputStreamPolling.sequencediagram">
      <SubType>Content</SubType>
    </Content>
    <Content Include="SequenceInputStreamPolling.sequencediagram.layout">
      <SubType>Content</SubType>
      <DependentUpon>SequenceInputStreamPolling.sequencediagram</DependentUpon>
    </Content>
    <Content Include="SequenceInputStreamTimer.sequencediagram">
      <SubType>Content</SubType>
    </Content>
    <Content Include="SequenceInputStreamTimer.sequencediagram.layout">
      <SubType>Content</SubType>
      <DependentUpon>SequenceInputStreamTimer.sequencediagram</DependentUpon>
    </Content>
    <Folder Include="ModelDefinition\" />
    <Content Include="ModelDefinition\models_supra.uml">
      <SubType>Content</SubType>
    </Content>
  </ItemGroup>
</Project>