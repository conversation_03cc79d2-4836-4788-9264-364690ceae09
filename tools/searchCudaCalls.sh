#!/bin/bash

find ../src/ "(" -iname "*.cu" -or -iname "*.c"  -or -iname "*.cpp"  -or -iname "*.cuh" -or -iname "*.h" ")" -exec grep -Hn -E "(cuda[A-Z])|(<[[:space:]]*<[[:space:]]*<)" {} \; | grep -v "cudaSafeCall(cudaStreamSynchronize(cudaStreamPerThread));" | grep -v "cudaSafeCall(cudaPeekAtLastError());" | grep -v '#include "utilities/cudaUtility.h"' | grep -v '#include <utilities/cudaUtility.h>' | grep -v -E "cudaStreamDefault[[:space:]]*>[[:space:]]*>[[:space:]]*>" | grep -v "cudaGetErrorString" | grep -v "cudaSuccess" | grep -v "#define cudaSafeCall(_err_) cudaSafeCall2" | grep -v "/// returns true if no error occured, false otherwise. Calles by cudaSafeCall" | grep -v "inline bool cudaSafeCall2" | grep -v "cudaStreamDefault" | grep -v "cudaMalloc" | grep -v "cudaFree"